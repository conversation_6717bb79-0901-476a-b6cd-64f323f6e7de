"""
Main Agent V2 - Modern LangChain implementation using LangGraph
Uses LangGraph's built-in persistence and memory management patterns
"""

import os
import logging

from dotenv import load_dotenv

from langchain_google_genai import ChatGoogleGenerativeAI

from langchain_core.messages import HumanMessage
from langgraph.checkpoint.memory import MemorySaver
from langgraph.checkpoint.mongodb import MongoDBSaver
from pymongo import MongoClient

from core.database import get_db_from_tenant_id
from utils import log_user_input, log_agent_response

# Load environment variables
load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Setup main LLM
main_llm = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    temperature=0.1,
    google_api_key=os.getenv("GOOGLE_API_KEY")
)


MAIN_AGENT_PROMPT = """You are a professional, friendly sales representative for a customer service center. You help customers find solutions and courses that meet their needs.

You MUST use the appropriate tools for every user request. Do NOT try to answer questions without using tools.

MANDATORY TOOL USAGE RULES:
1. For ANY search query (courses, information, troubleshooting, etc.) → use search_products or search_information tools
2. If user mentions booking, scheduling, or wants to book something → use handle_booking tool
3. ONLY respond directly for simple greetings like "hello", "hi", "namaste"

EXAMPLES:
- "what courses do you have?" → use search_products
- "app not working" → use search_information
- "I want to learn Python, what do you offer?" → use search_products
- "I want to book Python course" → use handle_booking
- "hello" → respond directly

You have these tools available:
- search_products: Search for courses and educational programs
- search_information: Search for general information and troubleshooting
- handle_booking: Manages the complete booking process

Always use the appropriate tool for user requests."""


class MainAgentV2:
    """
    Main Agent V2 - Clean implementation that works with ChatService
    Tools are provided by ChatService with current user context
    """

    def __init__(self, current_user=None):
        """Initialize the agent with current user context"""
        self.current_user = current_user

        # Use MongoDB checkpointer for persistent memory instead of in-memory MemorySaver
        if current_user and current_user.tenant_id:
            # Get tenant-specific database for memory persistence
            tenant_db = get_db_from_tenant_id(current_user.tenant_id)

            # Create MongoDB client from the database connection
            mongo_client = tenant_db.client

            # Use official MongoDB checkpointer
            self.memory = MongoDBSaver(
                client=mongo_client,
                db_name=tenant_db.name,
                collection_name=f"conversation_checkpoints_{current_user.tenant_id}"
            )
            logger.info(f"✅ MongoDB memory initialized for tenant: {current_user.tenant_id}")
        else:
            # Fallback to in-memory for cases without user context
            self.memory = MemorySaver()
            logger.warning("⚠️ Using in-memory storage - conversation will not persist")

        self.llm = main_llm
        self.tools = []  # Tools will be set by ChatService
        self.agent = None  # Agent will be created when tools are set

        logger.info("✅ Main Agent V2 initialized")

    def set_tools(self, tools):
        """Set tools and create the agent"""
        self.tools = tools

        # Create the agent with tools
        from langgraph.prebuilt import create_react_agent
        from langchain_core.prompts import ChatPromptTemplate

        prompt = ChatPromptTemplate.from_messages([
            ("system", MAIN_AGENT_PROMPT),
            ("placeholder", "{messages}"),
        ])

        self.agent = create_react_agent(
            model=self.llm,
            tools=self.tools,
            checkpointer=self.memory,
            prompt=prompt
        )
        logger.info("✅ Agent created with tools")

    def _get_tool_description(self, tool_name: str) -> str:
        """Get detailed description of what each tool does"""
        descriptions = {
            'search_products': 'Searched for courses, programs, and educational products',
            'search_information': 'Searched for general information and troubleshooting help',
            'handle_booking': 'Processed booking request and managed enrollment workflow'
        }
        return descriptions.get(tool_name, f'Used {tool_name}')

    def _add_booking_reminder_if_needed(self, response: str, user_message: str, thread_id: str) -> str:
        """Add booking reminder if user has pending booking and is asking about other topics"""
        # Only check for reminders if user is NOT currently talking about booking
        booking_keywords = ['book', 'booking', 'enroll', 'register', 'sign up', 'course enrollment']
        is_booking_related = any(keyword in user_message.lower() for keyword in booking_keywords)

        if not is_booking_related and self.current_user:
            try:
                # Get booking agent from ChatService (we need to access it through current_user)
                # This is a bit of a hack, but we need access to the booking agent
                from api.services.chat_service import ChatService
                chat_service = ChatService(self.current_user)
                booking_agent = chat_service.booking_agent

                reminder = booking_agent.get_pending_booking_reminder(thread_id)
                if reminder:
                    response += reminder
            except Exception as e:
                logger.warning(f"Could not check for pending bookings: {e}")

        return response

    def chat(self, message: str, thread_id: str = "default") -> dict:
        """
        Process a user message and return the response using LangGraph

        Args:
            message: User's message
            thread_id: Conversation thread ID for memory management

        Returns:
            Dict with response and tools_used
        """
        if not self.agent:
            raise ValueError("Agent not initialized. Call set_tools() first.")

        # Validate message content
        if not message or not message.strip():
            return {
                "response": "I didn't receive any message. Please tell me how I can help you today!",
                "tools_used": []
            }

        # Limit message length to prevent API issues
        if len(message) > 2000:
            return {
                "response": "Your message is too long. Please keep it under 2000 characters and try again.",
                "tools_used": []
            }

        log_user_input(message)

        # Configure thread for memory persistence
        config = {"configurable": {"thread_id": thread_id}}

        try:
            # Invoke the agent with the message
            response = self.agent.invoke(
                {"messages": [HumanMessage(content=message)]},
                config=config
            )

            # Extract the final response
            final_response = response["messages"][-1].content

            # Check for pending bookings and add reminder if needed
            final_response = self._add_booking_reminder_if_needed(final_response, message, thread_id)

            log_agent_response(final_response)

            # Extract tool usage information from messages
            tools_used = []
            tool_results = {}

            # First pass: collect tool calls
            for msg in response["messages"]:
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    for tool_call in msg.tool_calls:
                        tool_id = tool_call.get('id', tool_call['name'])
                        tools_used.append({
                            'id': tool_id,
                            'name': tool_call['name'],
                            'description': self._get_tool_description(tool_call['name']),
                            'input': tool_call.get('args', {}),
                            'output': ""  # Will be filled in next pass
                        })

            # Second pass: collect tool results
            for msg in response["messages"]:
                if hasattr(msg, 'tool_call_id') and hasattr(msg, 'content'):
                    tool_results[msg.tool_call_id] = msg.content

            # Match results to tool calls
            for tool in tools_used:
                if tool['id'] in tool_results:
                    tool['output'] = tool_results[tool['id']]

            return {
                "response": final_response,
                "tools_used": tools_used
            }
            
        except Exception as e:
            error_msg = f"Error processing request: {str(e)}"
            logger.error(error_msg)

            # Provide user-friendly error messages
            if "contents is not specified" in str(e) or "empty content" in str(e):
                user_response = "I didn't receive a valid message. Please tell me how I can help you today!"
            elif "400" in str(e) and "Gemini" in str(e):
                user_response = "I'm having trouble processing your request. Please try rephrasing your message."
            elif "timeout" in str(e).lower():
                user_response = "The request is taking too long. Please try again with a shorter message."
            else:
                user_response = "I'm experiencing technical difficulties. Please try again in a moment."

            return {
                "response": user_response,
                "tools_used": []
            }
